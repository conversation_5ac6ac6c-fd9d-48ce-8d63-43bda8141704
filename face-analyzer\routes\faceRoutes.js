const express = require('express');
const router = express.Router();
const {
  uploadImage,
  startAnalysis,
  getAnalysisResult,
  analyzeFace,

} = require('../controllers/faceController');

router.post('/upload', uploadImage);           
router.post('/start-analysis', startAnalysis); 
router.get('/result/:taskId', getAnalysisResult); 
// Main analysis endpoint
router.post('/analyze', analyzeFace);



module.exports = router;
